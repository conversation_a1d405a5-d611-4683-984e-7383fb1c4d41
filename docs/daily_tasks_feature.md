# 每日任务功能模块

## 功能概述

每日任务功能模块为用户提供了一套完整的任务系统，鼓励用户每天与应用进行互动。用户可以通过完成各种任务来获得经验值奖励，提升用户参与度和应用活跃度。

## 功能特性

### 1. 任务类型
- **开启对话**: 与任意AI角色发起第一次对话
- **雷达探索**: 点击一次雷达扫描功能
- **个人档案**: 浏览个人资料页面
- **搜索探索**: 使用搜索功能查找内容
- **消息中心**: 查看消息列表
- **深度对话**: 完成一次完整对话（发送5条消息）
- **探索发现**: 浏览探索页面

### 2. 核心功能
- **自动检测**: 用户执行相关操作时自动完成任务
- **进度跟踪**: 支持多步骤任务的进度跟踪
- **每日重置**: 每天零点自动重置所有任务
- **本地存储**: 使用SharedPreferences进行数据持久化
- **奖励系统**: 完成任务获得经验值奖励
- **多语言支持**: 支持中文和英文界面

### 3. 用户界面
- **任务卡片**: 在探索页面显示任务概览
- **任务面板**: 完整的任务列表和详情页面
- **进度显示**: 实时显示任务完成进度
- **视觉反馈**: 任务完成时的动画和状态变化

## 技术实现

### 文件结构
```
lib/
├── models/
│   └── daily_task.dart              # 任务数据模型
├── services/
│   └── daily_task_service.dart      # 任务服务类
├── components/
│   └── daily_task_card.dart         # 任务卡片组件
├── pages/
│   └── daily_task_panel_page.dart   # 任务面板页面
└── l10n/
    ├── app_en.arb                   # 英文国际化
    └── app_zh.arb                   # 中文国际化
```

### 核心类说明

#### DailyTask 模型
- 包含任务的所有属性：ID、类型、标题、描述、状态、进度等
- 支持JSON序列化和反序列化
- 提供任务状态管理方法

#### DailyTaskService 服务
- 单例模式，管理所有任务相关操作
- 负责任务创建、状态更新、本地存储
- 实现每日重置逻辑和统计功能

#### DailyTaskCard 组件
- 在探索页面显示任务概览
- 显示完成进度和活跃任务预览
- 提供跳转到任务面板的入口

#### DailyTaskPanelPage 页面
- 显示完整的任务列表
- 支持下拉刷新
- 展示任务详情和奖励信息

### 任务检测机制

任务完成检测分布在应用的各个关键页面：

1. **聊天页面** (`chat_page.dart`)
   - 检测第一次对话任务
   - 跟踪深度对话任务进度

2. **AI角色页面** (`ai_role_page.dart`)
   - 检测雷达扫描任务

3. **个人资料页面** (`profile_page.dart`)
   - 检测个人档案浏览任务

4. **聊天列表页面** (`chat_list_page.dart`)
   - 检测消息列表查看任务
   - 检测搜索功能使用任务

5. **AI详情页面** (`ai_detail_page.dart`)
   - 检测AI角色详情查看任务

6. **探索页面** (`discovery_page.dart`)
   - 检测探索页面浏览任务

## 使用方法

### 用户操作流程

1. **查看任务**
   - 在探索页面查看每日任务卡片
   - 点击卡片进入任务面板查看详情

2. **完成任务**
   - 按照任务描述执行相应操作
   - 系统自动检测并更新任务状态

3. **获得奖励**
   - 任务完成后自动获得经验值奖励
   - 在任务面板中查看完成状态

### 开发者集成

1. **初始化服务**
```dart
// 在main.dart中初始化
await DailyTaskService.init();
```

2. **添加任务检测**
```dart
// 在相关页面添加任务完成检测
await DailyTaskService.instance.completeTask(DailyTaskType.firstChat);
```

3. **更新任务进度**
```dart
// 对于多步骤任务
await DailyTaskService.instance.incrementTaskProgress(DailyTaskType.completeConversation);
```

## 数据存储

### SharedPreferences 键值
- `daily_tasks`: 存储任务列表的JSON数据
- `last_reset_date`: 记录最后重置日期
- `task_completion_stats`: 存储任务完成统计

### 数据格式
任务数据以JSON格式存储，包含所有任务属性和状态信息。

## 测试

项目包含完整的单元测试，覆盖：
- 任务创建和状态管理
- 进度更新和完成检测
- 数据序列化和持久化
- 任务重置逻辑

运行测试：
```bash
flutter test test/daily_task_test.dart
```

## 国际化支持

功能完全支持多语言，包括：
- 任务标题和描述
- 界面文本
- 奖励信息

当前支持语言：
- 中文（简体/繁体）
- 英文

## 性能优化

- 使用单例模式减少服务实例创建
- 异步操作避免阻塞UI线程
- 本地缓存减少重复计算
- 错误处理确保功能稳定性

## 未来扩展

可以考虑的功能扩展：
- 更多任务类型
- 任务难度等级
- 连续完成奖励
- 任务成就系统
- 社交分享功能
