import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ai_chat_app/models/daily_task.dart';
import 'package:ai_chat_app/services/daily_task_service.dart';

void main() {
  group('Daily Task Tests', () {
    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      await DailyTaskService.init();
    });

    tearDown(() async {
      // 清理测试数据
      await DailyTaskService.instance.clearAllData();
    });

    test('should create default tasks on first run', () async {
      final tasks = await DailyTaskService.instance.getAllTasks();
      
      expect(tasks.length, greaterThan(0));
      expect(tasks.every((task) => task.status == DailyTaskStatus.notStarted), true);
    });

    test('should complete a task', () async {
      // 完成第一次对话任务
      await DailyTaskService.instance.completeTask(DailyTaskType.firstChat);
      
      final tasks = await DailyTaskService.instance.getAllTasks();
      final chatTask = tasks.firstWhere((task) => task.type == DailyTaskType.firstChat);
      
      expect(chatTask.isCompleted, true);
      expect(chatTask.completedAt, isNotNull);
    });

    test('should update task progress', () async {
      // 更新深度对话任务进度
      await DailyTaskService.instance.incrementTaskProgress(DailyTaskType.completeConversation);
      
      final tasks = await DailyTaskService.instance.getAllTasks();
      final conversationTask = tasks.firstWhere((task) => task.type == DailyTaskType.completeConversation);
      
      expect(conversationTask.currentCount, 1);
      expect(conversationTask.progress, 0.2); // 1/5
      expect(conversationTask.status, DailyTaskStatus.inProgress);
    });

    test('should complete task when reaching target count', () async {
      // 增加5次进度，完成深度对话任务
      for (int i = 0; i < 5; i++) {
        await DailyTaskService.instance.incrementTaskProgress(DailyTaskType.completeConversation);
      }
      
      final tasks = await DailyTaskService.instance.getAllTasks();
      final conversationTask = tasks.firstWhere((task) => task.type == DailyTaskType.completeConversation);
      
      expect(conversationTask.isCompleted, true);
      expect(conversationTask.currentCount, 5);
      expect(conversationTask.progress, 1.0);
    });

    test('should get active tasks only', () async {
      // 完成一个任务
      await DailyTaskService.instance.completeTask(DailyTaskType.firstChat);
      
      final allTasks = await DailyTaskService.instance.getAllTasks();
      final activeTasks = await DailyTaskService.instance.getActiveTasks();
      final completedTasks = await DailyTaskService.instance.getCompletedTasks();
      
      expect(activeTasks.length, allTasks.length - 1);
      expect(completedTasks.length, 1);
      expect(activeTasks.every((task) => !task.isCompleted), true);
      expect(completedTasks.every((task) => task.isCompleted), true);
    });

    test('should calculate task overview correctly', () async {
      // 完成两个任务
      await DailyTaskService.instance.completeTask(DailyTaskType.firstChat);
      await DailyTaskService.instance.completeTask(DailyTaskType.radarScan);
      
      final overview = await DailyTaskService.instance.getTaskOverview();
      
      expect(overview['totalTasks'], greaterThan(0));
      expect(overview['completedTasks'], 2);
      expect(overview['activeTasks'], overview['totalTasks'] - 2);
      expect(overview['completionRate'], 2 / overview['totalTasks']);
    });

    test('should persist task completion stats', () async {
      // 完成任务并检查统计
      await DailyTaskService.instance.completeTask(DailyTaskType.firstChat);
      await DailyTaskService.instance.completeTask(DailyTaskType.radarScan);
      
      final stats = await DailyTaskService.instance.getTaskCompletionStats();
      
      expect(stats['firstChat'], 1);
      expect(stats['radarScan'], 1);
    });

    test('should handle task reset logic', () async {
      // 完成一个任务
      await DailyTaskService.instance.completeTask(DailyTaskType.firstChat);

      // 验证任务已完成
      final tasksBeforeReset = await DailyTaskService.instance.getAllTasks();
      final completedTask = tasksBeforeReset.firstWhere((task) => task.type == DailyTaskType.firstChat);
      expect(completedTask.isCompleted, true);

      // 手动重置任务（模拟每日重置）
      final resetTasks = tasksBeforeReset.map((task) => task.reset()).toList();

      // 检查每个重置后的任务
      for (final task in resetTasks) {
        expect(task.status, DailyTaskStatus.notStarted, reason: 'Task ${task.id} status should be notStarted');
        expect(task.progress, 0.0, reason: 'Task ${task.id} progress should be 0.0');
        expect(task.currentCount, 0, reason: 'Task ${task.id} currentCount should be 0');
        expect(task.completedAt, null, reason: 'Task ${task.id} completedAt should be null');
      }
    });
  });

  group('Daily Task Model Tests', () {
    test('should create task with correct properties', () {
      final task = DailyTask(
        id: 'test_task',
        type: DailyTaskType.firstChat,
        title: 'Test Task',
        description: 'Test Description',
        status: DailyTaskStatus.notStarted,
        icon: Icons.chat,
        reward: 'Test Reward',
        createdAt: DateTime.now(),
      );

      expect(task.id, 'test_task');
      expect(task.type, DailyTaskType.firstChat);
      expect(task.isNotStarted, true);
      expect(task.isCompleted, false);
      expect(task.isInProgress, false);
    });

    test('should update progress correctly', () {
      final task = DailyTask(
        id: 'test_task',
        type: DailyTaskType.completeConversation,
        title: 'Test Task',
        description: 'Test Description',
        status: DailyTaskStatus.notStarted,
        icon: Icons.chat,
        targetCount: 5,
        createdAt: DateTime.now(),
      );

      final updatedTask = task.updateProgress(3);

      expect(updatedTask.currentCount, 3);
      expect(updatedTask.progress, 0.6);
      expect(updatedTask.status, DailyTaskStatus.inProgress);
      expect(updatedTask.isCompleted, false);

      final completedTask = task.updateProgress(5);
      expect(completedTask.isCompleted, true);
      expect(completedTask.completedAt, isNotNull);
    });

    test('should serialize and deserialize correctly', () {
      final originalTask = DailyTask(
        id: 'test_task',
        type: DailyTaskType.firstChat,
        title: 'Test Task',
        description: 'Test Description',
        status: DailyTaskStatus.completed,
        icon: Icons.chat,
        reward: 'Test Reward',
        progress: 1.0,
        currentCount: 1,
        targetCount: 1,
        completedAt: DateTime.now(),
        createdAt: DateTime.now(),
      );

      final json = originalTask.toJson();
      final deserializedTask = DailyTask.fromJson(json);

      expect(deserializedTask.id, originalTask.id);
      expect(deserializedTask.type, originalTask.type);
      expect(deserializedTask.status, originalTask.status);
      expect(deserializedTask.progress, originalTask.progress);
      expect(deserializedTask.currentCount, originalTask.currentCount);
    });
  });
}
