import 'package:flutter/material.dart';
import 'package:easy_refresh/easy_refresh.dart';
import '../theme/app_design_system.dart';
import '../components/grid_background.dart';
import '../components/top3_ranking_card.dart';
import '../components/daily_topic_card.dart';
import '../components/daily_task_card.dart';

import '../models/user.dart';
import '../models/topic.dart';
import '../models/daily_task.dart';
import '../services/ai_data_service.dart';
import '../services/daily_topic_service.dart';
import '../services/daily_task_service.dart';
import 'chat_page.dart';
import 'ai_detail_page.dart';
import 'full_ranking_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class DiscoveryPage extends StatefulWidget {
  const DiscoveryPage({super.key});

  @override
  State<DiscoveryPage> createState() => _DiscoveryPageState();
}

class _DiscoveryPageState extends State<DiscoveryPage>
    with AutomaticKeepAliveClientMixin {
  late EasyRefreshController _refreshController;

  List<User> _allUsers = [];
  List<User> _weeklyStars = [];
  List<User> _top3Users = [];
  List<User> _entertainmentUsers = [];
  List<User> _learningUsers = [];
  List<User> _workUsers = [];
  List<String> _hotTopics = [];
  List<DailyTopic> _dailyTopics = [];
  bool _isLoading = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _refreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    _loadDiscoveryData();
    _completeExploreDiscoveryTask();
  }

  /// 完成浏览探索页面任务
  Future<void> _completeExploreDiscoveryTask() async {
    try {
      await DailyTaskService.instance.completeTask(DailyTaskType.exploreDiscovery);
    } catch (e) {
      // 任务更新失败不影响正常功能
      debugPrint('Failed to complete explore discovery task: $e');
    }
  }

  /// 获取国际化的热门话题列表
  List<String> _getLocalizedHotTopics() {
    final l10n = AppLocalizations.of(context);
    if (l10n == null) {
      // 如果无法获取本地化，返回默认的英文话题
      return [
        'Future of AI Development',
        'How to Improve Work Efficiency',
        'Creative Writing Techniques',
        'Healthy Lifestyle',
        'Technology and Life',
        'Personal Growth and Development',
      ];
    }

    return [
      l10n.topicAIFuture,
      l10n.topicProductivity,
      l10n.topicCreativeWriting,
      l10n.topicHealthyLifestyle,
      l10n.topicTechLife,
      l10n.topicPersonalGrowth,
    ];
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadDiscoveryData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 加载所有用户数据
      _allUsers = await AIDataService().getAllUsers();

      // 加载本周之星（前5个用户）
      _weeklyStars = _allUsers.take(5).toList();

      // 加载Top 3排行榜用户
      _top3Users = await AIDataService().getTop3WeeklyUsers();

      // 按分类加载用户
      _entertainmentUsers = _allUsers
          .where((user) => user.tags.any((tag) =>
              tag.toLowerCase().contains('entertainment') ||
              tag.toLowerCase().contains('fun') ||
              tag.toLowerCase().contains('game')))
          .take(4)
          .toList();

      _learningUsers = _allUsers
          .where((user) => user.tags.any((tag) =>
              tag.toLowerCase().contains('education') ||
              tag.toLowerCase().contains('learn') ||
              tag.toLowerCase().contains('study')))
          .take(4)
          .toList();

      _workUsers = _allUsers
          .where((user) => user.tags.any((tag) =>
              tag.toLowerCase().contains('business') ||
              tag.toLowerCase().contains('work') ||
              tag.toLowerCase().contains('professional')))
          .take(4)
          .toList();

      // 获取国际化的热门话题
      _hotTopics = _getLocalizedHotTopics();

      // 加载今日话题
      _dailyTopics = await DailyTopicService().getTodayTopics();
    } catch (e) {
      debugPrint('Error loading discovery data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    await _loadDiscoveryData();
    _refreshController.finishRefresh();
  }

  void _startChat(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(aiUser: user),
      ),
    );
  }

  void _viewUserDetail(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIDetailPage(aiUser: user),
      ),
    );
  }

  /// 获取话题本地化文本映射
  Map<String, String> _getTopicLocalizations() {
    final l10n = AppLocalizations.of(context)!;
    return {
      // 显示文本
      'topicTeacherGoals': l10n.topicTeacherGoals,
      'topicArtistPainting': l10n.topicArtistPainting,
      'topicHeartbreakAdvice': l10n.topicHeartbreakAdvice,
      'topicCookingTips': l10n.topicCookingTips,
      'topicFitnessMotivation': l10n.topicFitnessMotivation,
      'topicTravelStories': l10n.topicTravelStories,
      'topicMusicDiscovery': l10n.topicMusicDiscovery,
      'topicBookRecommendations': l10n.topicBookRecommendations,
      'topicCareerAdvice': l10n.topicCareerAdvice,
      'topicMindfulness': l10n.topicMindfulness,
      // 详细描述文本
      'topicTeacherGoalsDetail': l10n.topicTeacherGoalsDetail,
      'topicArtistPaintingDetail': l10n.topicArtistPaintingDetail,
      'topicHeartbreakAdviceDetail': l10n.topicHeartbreakAdviceDetail,
      'topicCookingTipsDetail': l10n.topicCookingTipsDetail,
      'topicFitnessMotivationDetail': l10n.topicFitnessMotivationDetail,
      'topicTravelStoriesDetail': l10n.topicTravelStoriesDetail,
      'topicMusicDiscoveryDetail': l10n.topicMusicDiscoveryDetail,
      'topicBookRecommendationsDetail': l10n.topicBookRecommendationsDetail,
      'topicCareerAdviceDetail': l10n.topicCareerAdviceDetail,
      'topicMindfulnessDetail': l10n.topicMindfulnessDetail,
    };
  }

  /// 处理话题点击事件
  Future<void> _onTopicTap(DailyTopic topic) async {
    final l10n = AppLocalizations.of(context)!;

    // 获取话题的详细描述文本（用于聊天预填充）
    final detailText = DailyTopicService().getTopicDetailText(
      topic,
      Localizations.localeOf(context),
      _getTopicLocalizations(),
    );

    // 如果话题有指定的AI用户，直接跳转到该用户
    if (topic.aiUserId != null) {
      final aiUser = await DailyTopicService().getAIUserForTopic(topic);
      if (aiUser != null) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatPage(
              aiUser: aiUser,
              initialMessage: detailText,
            ),
          ),
        );
        return;
      }
    }

    // 如果没有指定AI用户，选择一个随机用户
    if (_allUsers.isNotEmpty) {
      final randomUser = _allUsers[DateTime.now().millisecondsSinceEpoch % _allUsers.length];
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChatPage(
            aiUser: randomUser,
            initialMessage: detailText,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      body: GridBackground(
        gridSize: 35.0,
        gridColor: Colors.black.withAlpha(20),
        strokeWidth: 0.5,
        child: CustomScrollView(
          slivers: [
            // 顶部标题 - 更大更突出
            SliverToBoxAdapter(
              child: Padding(
                  padding: const EdgeInsets.fromLTRB(15, 65, 24, 20),
                  child: UnconstrainedBox(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 15, vertical: 10),
                      decoration: BoxDecoration(
                        color: AppDesignSystem.profileCardBackground,
                        borderRadius: BorderRadius.all(Radius.circular(50)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.explore,
                            size: 28,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 10),
                          Text(
                            AppLocalizations.of(context)!.discovery,
                            style: const TextStyle(
                              color: AppDesignSystem.textQuaternary,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: -0.5,
                            ),
                          )
                        ],
                      ),
                    ),
                  )),
            ),

            // 今日话题模块
            if (_dailyTopics.isNotEmpty)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: AppDesignSystem.spaceL),
                  child: DailyTopicsSection(
                    topics: _dailyTopics,
                    localizations: _getTopicLocalizations(),
                    locale: Localizations.localeOf(context),
                    onTopicTap: _onTopicTap,
                    sectionTitle: AppLocalizations.of(context)!.dailyTopics,
                  ),
                ),
              ),

            // 每日任务模块
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.only(bottom: AppDesignSystem.spaceL),
                child: DailyTaskCard(),
              ),
            ),

            // Top 3 排行榜模块
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTop3SectionHeader(context),
                    Top3RankingCard(
                      top3Users: _top3Users,
                      onRefresh: _onRefresh,
                    )
                  ],
                ),
              ),
            ),

            // 本周之星模块 - 重新设计为大卡片
            _buildWeeklyStarsSection(),

            // 历史长河中的智者模块
            _buildHistoricalWiseSection(),

            // 科幻宇宙的探险家模块
            _buildSciFiExplorersSection(),

            // 底部间距
            const SliverToBoxAdapter(
              child: SizedBox(height: 100),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 5),
              Text(
                title,
                style: const TextStyle(
                  color: AppDesignSystem.textPrimary,
                  fontSize: 20,
                  fontWeight: AppDesignSystem.fontWeightBold,
                ),
              ),
              const SizedBox(width: 5),
            ],
          ),
          Container(
            width: 50,
            height: 4,
            margin: const EdgeInsets.only(top: 6, left: 5),
            decoration: BoxDecoration(
              color: AppDesignSystem.primaryYellow,
              borderRadius: BorderRadius.circular(3),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildTop3SectionHeader(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(width: 5),
                  Text(
                    AppLocalizations.of(context)!.weeklyTop3,
                    style: const TextStyle(
                      color: AppDesignSystem.textPrimary,
                      fontSize: 20,
                      fontWeight: AppDesignSystem.fontWeightSemiBold,
                    ),
                  ),
                ],
              ),
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () => _navigateToFullRankings(context),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.seeAll,
                          style: TextStyle(
                            color: AppDesignSystem.textHint,
                            fontSize: 14,
                            fontWeight: AppDesignSystem.fontWeightMedium,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: AppDesignSystem.textHint,
                          size: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToFullRankings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FullRankingPage(),
      ),
    );
  }

  Widget _buildWeeklyStarsSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.fromLTRB(15, 0, 15, 32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 本周之星标签
            _buildSectionHeader(
                context, AppLocalizations.of(context)!.weeklyStars),
            const SizedBox(height: 20),

            if (_isLoading)
              const SizedBox(
                height: 200,
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_weeklyStars.isEmpty)
              SizedBox(
                height: 200,
                child: Center(
                  child: Text(
                    '暂无推荐内容',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ),
              )
            else
              _buildLargeUserCard(_weeklyStars.first),
          ],
        ),
      ),
    );
  }

  Widget _buildLargeUserCard(User user) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _viewUserDetail(user),
          child: Container(
            height: 250,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppDesignSystem.primaryBlack,
                width: 2,
              ),
            ),
            child: Stack(
              children: [
                // 大头像
                Positioned.fill(
                    child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(user.avatar),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                )),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(15, 0, 0, 10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        colors: [
                          Colors.black.withOpacity(0.8),
                          Colors.black.withOpacity(0.6),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(height: 20),
                        // 大名字，类似参考图片中的"Zoe"
                        Text(
                          user.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            height: 1.0,
                          ),
                        ),
                        const SizedBox(height: 4),

                        // 角色描述
                        Text(
                          user.getRoleAndPersonality(context),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            height: 1.3,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHistoricalWiseSection() {
    // 筛选历史人物类型的AI角色
    final historicalUsers = _allUsers
        // .where((user) => user.tags.any((tag) =>
        //     tag.toLowerCase().contains('historical') ||
        //     tag.toLowerCase().contains('philosopher') ||
        //     tag.toLowerCase().contains('wise') ||
        //     user.name.toLowerCase().contains('confucius') ||
        //     user.name.toLowerCase().contains('da vinci') ||
        //     user.name.toLowerCase().contains('cleopatra')))
        .take(3)
        .toList();

    if (historicalUsers.isEmpty)
      return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '历史长河中的智者', // 可以后续添加到国际化
              style: const TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 6),
              height: 4,
              color: AppDesignSystem.primaryYellow,
              width: 50,
            ),
            Row(
              children: historicalUsers
                  .map((user) => Expanded(
                        child: Container(
                          child: _buildCircularUserCard(user),
                        ),
                      ))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSciFiExplorersSection() {
    // 筛选科幻类型的AI角色
    final sciFiUsers = _allUsers
        .where((user) => user.tags.any((tag) =>
            tag.toLowerCase().contains('sci-fi') ||
            tag.toLowerCase().contains('science') ||
            tag.toLowerCase().contains('technology') ||
            tag.toLowerCase().contains('future')))
        .take(2)
        .toList();

    if (sciFiUsers.isEmpty)
      return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '科幻宇宙的探险家', // 可以后续添加到国际化
              style: const TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 6, bottom: 16),
              height: 4,
              color: AppDesignSystem.primaryYellow,
              width: 50,
            ),
            Row(
              children: sciFiUsers
                  .map((user) => _buildHorizontalUserCard(user))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircularUserCard(User user) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _viewUserDetail(user),
        child: Container(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: [
              // 圆形头像
              CircleAvatar(
                radius: 60,
                backgroundImage: AssetImage(user.avatar),
              ),
              // 名字
              Text(
                user.name,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),

              // 角色描述
              Text(
                user.role,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHorizontalUserCard(User user) {
    return Expanded(
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _viewUserDetail(user),
        child: Container(
          margin: EdgeInsets.only(right: 15),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 12,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头像
              CircleAvatar(
                radius: 25,
                backgroundImage: AssetImage(user.avatar),
              ),
              const SizedBox(width: 16),

              // 用户信息
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Container(
                    constraints: BoxConstraints(maxWidth: 50),
                    child: Text(
                      user.role,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryRecommendationsSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.categoryRecommendations,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // 娱乐分类
            _buildCategorySection(
              AppLocalizations.of(context)!.entertainment,
              _entertainmentUsers,
              Icons.movie,
            ),
            const SizedBox(height: 24),

            // 学习分类
            _buildCategorySection(
              AppLocalizations.of(context)!.learning,
              _learningUsers,
              Icons.school,
            ),
            const SizedBox(height: 24),

            // 工作分类
            _buildCategorySection(
              AppLocalizations.of(context)!.work,
              _workUsers,
              Icons.work,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection(String title, List<User> users, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppDesignSystem.primaryYellowAccent,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_isLoading)
          const SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        else if (users.isEmpty)
          SizedBox(
            height: 100,
            child: Center(
              child: Text(
                '暂无推荐内容',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          )
        else
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return _buildCategoryUserCard(user);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildCategoryUserCard(User user) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      child: GestureDetector(
        onTap: () => _viewUserDetail(user),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 头像
            CircleAvatar(
              radius: 25,
              backgroundImage: AssetImage(user.avatar),
            ),
            const SizedBox(height: 6),

            // 名称
            Text(
              user.name,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotTopicsSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.hotTopics,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children:
                  _hotTopics.map((topic) => _buildTopicChip(topic)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicChip(String topic) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () {
          // TODO: 实现话题点击功能，可以导航到相关的AI角色或开始相关话题的聊天
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('点击了话题: $topic'),
              duration: const Duration(seconds: 1),
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppDesignSystem.primaryYellow,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Text(
            topic,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
