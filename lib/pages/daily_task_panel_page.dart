import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';
import '../models/daily_task.dart';
import '../services/daily_task_service.dart';
import '../components/grid_background.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 每日任务面板页面
/// 显示完整的任务列表，包含任务详情、完成状态、奖励信息等
class DailyTaskPanelPage extends StatefulWidget {
  const DailyTaskPanelPage({super.key});

  @override
  State<DailyTaskPanelPage> createState() => _DailyTaskPanelPageState();
}

class _DailyTaskPanelPageState extends State<DailyTaskPanelPage>
    with TickerProviderStateMixin {
  List<DailyTask> _allTasks = [];
  Map<String, dynamic> _taskOverview = {};
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppDesignSystem.animationNormal,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppDesignSystem.animationCurveDefault,
    ));
    
    _loadTaskData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadTaskData() async {
    try {
      final allTasks = await DailyTaskService.instance.getAllTasks();
      final overview = await DailyTaskService.instance.getTaskOverview();
      
      if (mounted) {
        setState(() {
          _allTasks = allTasks;
          _taskOverview = overview;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshTasks() async {
    await _loadTaskData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      body: GridBackground(
        gridSize: 35.0,
        gridColor: Colors.black.withAlpha(20),
        strokeWidth: 0.5,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _isLoading ? _buildLoadingView() : _buildTaskList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDesignSystem.spaceL),
      child: Column(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(AppDesignSystem.spaceS),
                  decoration: BoxDecoration(
                    color: AppDesignSystem.backgroundCard,
                    borderRadius: AppDesignSystem.borderRadiusS,
                    boxShadow: [AppDesignSystem.shadowLight],
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: AppDesignSystem.textPrimary,
                    size: AppDesignSystem.iconSizeL,
                  ),
                ),
              ),
              const SizedBox(width: AppDesignSystem.spaceM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.dailyInteractiveTasks,
                      style: const TextStyle(
                        color: AppDesignSystem.textPrimary,
                        fontSize: AppDesignSystem.fontSizeXXL,
                        fontWeight: AppDesignSystem.fontWeightBold,
                      ),
                    ),
                    const SizedBox(height: AppDesignSystem.spaceXXS),
                    Text(
                      AppLocalizations.of(context)!.completeTasksToEarnRewards,
                      style: TextStyle(
                        color: AppDesignSystem.textSecondary,
                        fontSize: AppDesignSystem.fontSizeRegular,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDesignSystem.spaceL),
          _buildProgressOverview(),
        ],
      ),
    );
  }

  Widget _buildProgressOverview() {
    final totalTasks = _taskOverview['totalTasks'] ?? 0;
    final completedTasks = _taskOverview['completedTasks'] ?? 0;
    final completionRate = _taskOverview['completionRate'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(AppDesignSystem.spaceL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppDesignSystem.primaryYellow.withOpacity(0.1),
            AppDesignSystem.primaryYellowAccent.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: AppDesignSystem.borderRadiusL,
        border: Border.all(
          color: AppDesignSystem.primaryYellow.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.task_alt,
                  label: '总任务',
                  value: totalTasks.toString(),
                  color: AppDesignSystem.textPrimary,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.check_circle,
                  label: '已完成',
                  value: completedTasks.toString(),
                  color: AppDesignSystem.success,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.schedule,
                  label: '进行中',
                  value: (totalTasks - completedTasks).toString(),
                  color: AppDesignSystem.warning,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDesignSystem.spaceM),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '今日完成率',
                    style: TextStyle(
                      color: AppDesignSystem.textPrimary,
                      fontSize: AppDesignSystem.fontSizeMedium,
                      fontWeight: AppDesignSystem.fontWeightMedium,
                    ),
                  ),
                  Text(
                    '${(completionRate * 100).toInt()}%',
                    style: const TextStyle(
                      color: AppDesignSystem.primaryYellow,
                      fontSize: AppDesignSystem.fontSizeMedium,
                      fontWeight: AppDesignSystem.fontWeightBold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDesignSystem.spaceS),
              LinearProgressIndicator(
                value: completionRate,
                backgroundColor: AppDesignSystem.textSecondary.withOpacity(0.2),
                valueColor: const AlwaysStoppedAnimation<Color>(AppDesignSystem.primaryYellow),
                borderRadius: BorderRadius.circular(4),
                minHeight: 8,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: AppDesignSystem.iconSizeL,
        ),
        const SizedBox(height: AppDesignSystem.spaceXS),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: AppDesignSystem.fontSizeLarge,
            fontWeight: AppDesignSystem.fontWeightBold,
          ),
        ),
        const SizedBox(height: AppDesignSystem.spaceXXS),
        Text(
          label,
          style: TextStyle(
            color: AppDesignSystem.textSecondary,
            fontSize: AppDesignSystem.fontSizeSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppDesignSystem.primaryYellow),
      ),
    );
  }

  Widget _buildTaskList() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _refreshTasks,
        color: AppDesignSystem.primaryYellow,
        backgroundColor: AppDesignSystem.backgroundCard,
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
          itemCount: _allTasks.length,
          itemBuilder: (context, index) {
            final task = _allTasks[index];
            return _buildTaskItem(task, index);
          },
        ),
      ),
    );
  }

  Widget _buildTaskItem(DailyTask task, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDesignSystem.spaceM),
      child: AnimatedContainer(
        duration: AppDesignSystem.animationNormal,
        padding: const EdgeInsets.all(AppDesignSystem.spaceL),
        decoration: BoxDecoration(
          color: task.isCompleted 
              ? AppDesignSystem.success.withOpacity(0.1)
              : AppDesignSystem.backgroundCard,
          borderRadius: AppDesignSystem.borderRadiusL,
          border: Border.all(
            color: task.isCompleted 
                ? AppDesignSystem.success.withOpacity(0.3)
                : AppDesignSystem.textSecondary.withOpacity(0.2),
            width: 1.5,
          ),
          boxShadow: [
            if (!task.isCompleted) AppDesignSystem.shadowLight,
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppDesignSystem.spaceS),
                  decoration: BoxDecoration(
                    color: task.isCompleted 
                        ? AppDesignSystem.success.withOpacity(0.2)
                        : AppDesignSystem.primaryYellow.withOpacity(0.2),
                    borderRadius: AppDesignSystem.borderRadiusS,
                  ),
                  child: Icon(
                    task.isCompleted ? Icons.check_circle : task.icon,
                    color: task.isCompleted 
                        ? AppDesignSystem.success
                        : AppDesignSystem.primaryYellow,
                    size: AppDesignSystem.iconSizeL,
                  ),
                ),
                const SizedBox(width: AppDesignSystem.spaceM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.title,
                        style: TextStyle(
                          color: task.isCompleted 
                              ? AppDesignSystem.success
                              : AppDesignSystem.textPrimary,
                          fontSize: AppDesignSystem.fontSizeLarge,
                          fontWeight: AppDesignSystem.fontWeightSemiBold,
                          decoration: task.isCompleted 
                              ? TextDecoration.lineThrough
                              : null,
                        ),
                      ),
                      const SizedBox(height: AppDesignSystem.spaceXXS),
                      Text(
                        task.description,
                        style: TextStyle(
                          color: AppDesignSystem.textSecondary,
                          fontSize: AppDesignSystem.fontSizeRegular,
                        ),
                      ),
                    ],
                  ),
                ),
                if (task.isCompleted)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDesignSystem.spaceS,
                      vertical: AppDesignSystem.spaceXS,
                    ),
                    decoration: BoxDecoration(
                      color: AppDesignSystem.success,
                      borderRadius: AppDesignSystem.borderRadiusS,
                    ),
                    child: const Text(
                      '已完成',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: AppDesignSystem.fontSizeSmall,
                        fontWeight: AppDesignSystem.fontWeightMedium,
                      ),
                    ),
                  ),
              ],
            ),
            if (task.reward != null) ...[
              const SizedBox(height: AppDesignSystem.spaceM),
              Container(
                padding: const EdgeInsets.all(AppDesignSystem.spaceS),
                decoration: BoxDecoration(
                  color: AppDesignSystem.primaryYellow.withOpacity(0.1),
                  borderRadius: AppDesignSystem.borderRadiusS,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: AppDesignSystem.primaryYellow,
                      size: AppDesignSystem.iconSizeS,
                    ),
                    const SizedBox(width: AppDesignSystem.spaceXS),
                    Text(
                      '奖励: ${task.reward}',
                      style: const TextStyle(
                        color: AppDesignSystem.primaryYellow,
                        fontSize: AppDesignSystem.fontSizeSmall,
                        fontWeight: AppDesignSystem.fontWeightMedium,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (task.targetCount > 1) ...[
              const SizedBox(height: AppDesignSystem.spaceM),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '进度',
                        style: TextStyle(
                          color: AppDesignSystem.textSecondary,
                          fontSize: AppDesignSystem.fontSizeSmall,
                        ),
                      ),
                      Text(
                        '${task.currentCount}/${task.targetCount}',
                        style: TextStyle(
                          color: task.isCompleted 
                              ? AppDesignSystem.success
                              : AppDesignSystem.primaryYellow,
                          fontSize: AppDesignSystem.fontSizeSmall,
                          fontWeight: AppDesignSystem.fontWeightMedium,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDesignSystem.spaceXS),
                  LinearProgressIndicator(
                    value: task.progress,
                    backgroundColor: AppDesignSystem.textSecondary.withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      task.isCompleted 
                          ? AppDesignSystem.success
                          : AppDesignSystem.primaryYellow,
                    ),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
