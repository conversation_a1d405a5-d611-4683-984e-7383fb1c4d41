/// 今日话题数据模型
class DailyTopic {
  final String id;
  final String emoji;
  final String textKey; // 国际化文本的key（用于显示）
  final String detailKey; // 国际化详细描述的key（用于聊天预填充）
  final String? aiUserId; // 对应的AI角色ID，可选
  final String category; // 话题分类
  final int priority; // 显示优先级，数字越小优先级越高

  const DailyTopic({
    required this.id,
    required this.emoji,
    required this.textKey,
    required this.detailKey,
    this.aiUserId,
    required this.category,
    this.priority = 0,
  });

  factory DailyTopic.fromJson(Map<String, dynamic> json) {
    return DailyTopic(
      id: json['id'] as String,
      emoji: json['emoji'] as String,
      textKey: json['textKey'] as String,
      detailKey: json['detailKey'] as String,
      aiUserId: json['aiUserId'] as String?,
      category: json['category'] as String,
      priority: json['priority'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'emoji': emoji,
      'textKey': textKey,
      'detailKey': detailKey,
      'aiUserId': aiUserId,
      'category': category,
      'priority': priority,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyTopic && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'DailyTopic(id: $id, emoji: $emoji, textKey: $textKey, aiUserId: $aiUserId, category: $category, priority: $priority)';
  }
}
