import 'package:flutter/material.dart';

/// 每日任务类型枚举
enum DailyTaskType {
  /// 与任意角色发起第一次对话
  firstChat,
  /// 点击一次雷达扫描功能
  radarScan,
  /// 浏览个人资料页面
  viewProfile,
  /// 使用搜索功能
  useSearch,
  /// 查看消息列表
  viewMessageList,
  /// 查看AI角色详情页面
  viewAIDetail,
  /// 完成一次完整对话（发送5条消息）
  completeConversation,
  /// 浏览探索页面
  exploreDiscovery,
}

/// 每日任务状态枚举
enum DailyTaskStatus {
  /// 未开始
  notStarted,
  /// 进行中
  inProgress,
  /// 已完成
  completed,
}

/// 每日任务数据模型
class DailyTask {
  /// 任务唯一标识符
  final String id;
  
  /// 任务类型
  final DailyTaskType type;
  
  /// 任务标题
  final String title;
  
  /// 任务描述
  final String description;
  
  /// 任务状态
  final DailyTaskStatus status;
  
  /// 任务图标
  final IconData icon;
  
  /// 任务奖励描述（可选）
  final String? reward;
  
  /// 任务进度（0.0 - 1.0）
  final double progress;
  
  /// 任务目标数量（如发送5条消息）
  final int targetCount;
  
  /// 当前完成数量
  final int currentCount;
  
  /// 任务完成时间戳
  final DateTime? completedAt;
  
  /// 任务创建时间戳
  final DateTime createdAt;

  const DailyTask({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.status,
    required this.icon,
    this.reward,
    this.progress = 0.0,
    this.targetCount = 1,
    this.currentCount = 0,
    this.completedAt,
    required this.createdAt,
  });

  /// 创建任务副本，允许修改部分属性
  DailyTask copyWith({
    String? id,
    DailyTaskType? type,
    String? title,
    String? description,
    DailyTaskStatus? status,
    IconData? icon,
    String? reward,
    double? progress,
    int? targetCount,
    int? currentCount,
    DateTime? completedAt,
    DateTime? createdAt,
  }) {
    return DailyTask(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      icon: icon ?? this.icon,
      reward: reward ?? this.reward,
      progress: progress ?? this.progress,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// 检查任务是否已完成
  bool get isCompleted => status == DailyTaskStatus.completed;

  /// 检查任务是否进行中
  bool get isInProgress => status == DailyTaskStatus.inProgress;

  /// 检查任务是否未开始
  bool get isNotStarted => status == DailyTaskStatus.notStarted;

  /// 更新任务进度
  DailyTask updateProgress(int newCount) {
    final updatedCount = newCount.clamp(0, targetCount);
    final updatedProgress = updatedCount / targetCount;
    final isNowCompleted = updatedCount >= targetCount;
    
    return copyWith(
      currentCount: updatedCount,
      progress: updatedProgress,
      status: isNowCompleted 
          ? DailyTaskStatus.completed 
          : (updatedCount > 0 ? DailyTaskStatus.inProgress : DailyTaskStatus.notStarted),
      completedAt: isNowCompleted ? DateTime.now() : null,
    );
  }

  /// 标记任务为已完成
  DailyTask markAsCompleted() {
    return copyWith(
      status: DailyTaskStatus.completed,
      progress: 1.0,
      currentCount: targetCount,
      completedAt: DateTime.now(),
    );
  }

  /// 重置任务状态（用于每日重置）
  DailyTask reset() {
    return copyWith(
      status: DailyTaskStatus.notStarted,
      progress: 0.0,
      currentCount: 0,
      completedAt: null,
      createdAt: DateTime.now(),
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'status': status.name,
      'icon': icon.codePoint,
      'reward': reward,
      'progress': progress,
      'targetCount': targetCount,
      'currentCount': currentCount,
      'completedAt': completedAt?.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  /// 从JSON格式创建任务实例
  factory DailyTask.fromJson(Map<String, dynamic> json) {
    return DailyTask(
      id: json['id'] as String,
      type: DailyTaskType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DailyTaskType.firstChat,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      status: DailyTaskStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => DailyTaskStatus.notStarted,
      ),
      icon: IconData(
        json['icon'] as int,
        fontFamily: 'MaterialIcons',
      ),
      reward: json['reward'] as String?,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      targetCount: json['targetCount'] as int? ?? 1,
      currentCount: json['currentCount'] as int? ?? 0,
      completedAt: json['completedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'] as int)
          : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] as int),
    );
  }

  @override
  String toString() {
    return 'DailyTask(id: $id, type: $type, title: $title, status: $status, progress: $progress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyTask && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
