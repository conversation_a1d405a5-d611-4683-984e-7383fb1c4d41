import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/daily_task.dart';

/// 每日任务服务类
/// 负责任务的创建、状态管理、本地存储、每日重置逻辑等核心功能
class DailyTaskService {
  static DailyTaskService? _instance;
  static SharedPreferences? _preferences;

  // SharedPreferences 键名
  static const String _keyTasks = 'daily_tasks';
  static const String _keyLastResetDate = 'last_reset_date';
  static const String _keyTaskCompletionStats = 'task_completion_stats';

  DailyTaskService._internal();

  /// 获取单例实例
  static DailyTaskService get instance {
    _instance ??= DailyTaskService._internal();
    return _instance!;
  }

  /// 初始化服务
  static Future<void> init() async {
    _preferences = await SharedPreferences.getInstance();
    await instance._checkAndResetDailyTasks();
  }

  /// 获取所有每日任务
  Future<List<DailyTask>> getAllTasks() async {
    await _checkAndResetDailyTasks();
    
    final tasksJson = _preferences?.getString(_keyTasks);
    if (tasksJson == null || tasksJson.isEmpty) {
      // 如果没有保存的任务，创建默认任务
      final defaultTasks = _createDefaultTasks();
      await _saveTasks(defaultTasks);
      return defaultTasks;
    }

    try {
      final List<dynamic> tasksList = json.decode(tasksJson);
      return tasksList.map((taskJson) => DailyTask.fromJson(taskJson)).toList();
    } catch (e) {
      // 如果解析失败，返回默认任务
      final defaultTasks = _createDefaultTasks();
      await _saveTasks(defaultTasks);
      return defaultTasks;
    }
  }

  /// 获取活跃任务（未完成的任务）
  Future<List<DailyTask>> getActiveTasks() async {
    final allTasks = await getAllTasks();
    return allTasks.where((task) => !task.isCompleted).toList();
  }

  /// 获取已完成任务
  Future<List<DailyTask>> getCompletedTasks() async {
    final allTasks = await getAllTasks();
    return allTasks.where((task) => task.isCompleted).toList();
  }

  /// 更新任务状态
  Future<void> updateTaskStatus(String taskId, DailyTaskStatus status) async {
    final tasks = await getAllTasks();
    final taskIndex = tasks.indexWhere((task) => task.id == taskId);
    
    if (taskIndex != -1) {
      tasks[taskIndex] = tasks[taskIndex].copyWith(
        status: status,
        completedAt: status == DailyTaskStatus.completed ? DateTime.now() : null,
      );
      await _saveTasks(tasks);
    }
  }

  /// 更新任务进度
  Future<void> updateTaskProgress(String taskId, int newCount) async {
    final tasks = await getAllTasks();
    final taskIndex = tasks.indexWhere((task) => task.id == taskId);
    
    if (taskIndex != -1) {
      tasks[taskIndex] = tasks[taskIndex].updateProgress(newCount);
      await _saveTasks(tasks);
    }
  }

  /// 完成指定类型的任务
  Future<void> completeTask(DailyTaskType taskType) async {
    final tasks = await getAllTasks();
    final taskIndex = tasks.indexWhere((task) => task.type == taskType && !task.isCompleted);
    
    if (taskIndex != -1) {
      tasks[taskIndex] = tasks[taskIndex].markAsCompleted();
      await _saveTasks(tasks);
      
      // 记录任务完成统计
      await _recordTaskCompletion(taskType);
    }
  }

  /// 增加指定类型任务的进度
  Future<void> incrementTaskProgress(DailyTaskType taskType) async {
    final tasks = await getAllTasks();
    final taskIndex = tasks.indexWhere((task) => task.type == taskType && !task.isCompleted);
    
    if (taskIndex != -1) {
      final currentTask = tasks[taskIndex];
      final newCount = currentTask.currentCount + 1;
      tasks[taskIndex] = currentTask.updateProgress(newCount);
      await _saveTasks(tasks);
      
      // 如果任务完成，记录统计
      if (tasks[taskIndex].isCompleted) {
        await _recordTaskCompletion(taskType);
      }
    }
  }

  /// 获取任务完成统计
  Future<Map<String, int>> getTaskCompletionStats() async {
    final statsJson = _preferences?.getString(_keyTaskCompletionStats);
    if (statsJson == null || statsJson.isEmpty) {
      return {};
    }

    try {
      final Map<String, dynamic> stats = json.decode(statsJson);
      return stats.map((key, value) => MapEntry(key, value as int));
    } catch (e) {
      return {};
    }
  }

  /// 检查并重置每日任务
  Future<void> _checkAndResetDailyTasks() async {
    final lastResetDateStr = _preferences?.getString(_keyLastResetDate);
    final today = DateTime.now();
    final todayStr = '${today.year}-${today.month}-${today.day}';

    if (lastResetDateStr != todayStr) {
      // 需要重置任务
      await _resetDailyTasks();
      await _preferences?.setString(_keyLastResetDate, todayStr);
    }
  }

  /// 重置每日任务
  Future<void> _resetDailyTasks() async {
    final defaultTasks = _createDefaultTasks();
    await _saveTasks(defaultTasks);
  }

  /// 保存任务到本地存储
  Future<void> _saveTasks(List<DailyTask> tasks) async {
    final tasksJson = json.encode(tasks.map((task) => task.toJson()).toList());
    await _preferences?.setString(_keyTasks, tasksJson);
  }

  /// 记录任务完成统计
  Future<void> _recordTaskCompletion(DailyTaskType taskType) async {
    final stats = await getTaskCompletionStats();
    final key = taskType.name;
    stats[key] = (stats[key] ?? 0) + 1;
    
    final statsJson = json.encode(stats);
    await _preferences?.setString(_keyTaskCompletionStats, statsJson);
  }

  /// 创建默认任务列表
  List<DailyTask> _createDefaultTasks() {
    final now = DateTime.now();

    return [
      DailyTask(
        id: 'task_first_chat',
        type: DailyTaskType.firstChat,
        title: 'Start Conversation', // 将在UI层使用国际化
        description: 'Start your first conversation with any AI character',
        status: DailyTaskStatus.notStarted,
        icon: Icons.chat_bubble_outline,
        reward: 'Experience Points +10',
        createdAt: now,
      ),
      DailyTask(
        id: 'task_radar_scan',
        type: DailyTaskType.radarScan,
        title: 'Radar Exploration',
        description: 'Use the radar scan feature once',
        status: DailyTaskStatus.notStarted,
        icon: Icons.radar,
        reward: 'Experience Points +5',
        createdAt: now,
      ),
      DailyTask(
        id: 'task_view_profile',
        type: DailyTaskType.viewProfile,
        title: 'Personal Profile',
        description: 'Browse your personal profile page',
        status: DailyTaskStatus.notStarted,
        icon: Icons.account_circle_outlined,
        reward: 'Experience Points +5',
        createdAt: now,
      ),
      DailyTask(
        id: 'task_use_search',
        type: DailyTaskType.useSearch,
        title: 'Search Exploration',
        description: 'Use the search function to find content',
        status: DailyTaskStatus.notStarted,
        icon: Icons.search,
        reward: 'Experience Points +5',
        createdAt: now,
      ),
      DailyTask(
        id: 'task_view_messages',
        type: DailyTaskType.viewMessageList,
        title: 'Message Center',
        description: 'Check your message list',
        status: DailyTaskStatus.notStarted,
        icon: Icons.message_outlined,
        reward: 'Experience Points +5',
        createdAt: now,
      ),
      DailyTask(
        id: 'task_complete_conversation',
        type: DailyTaskType.completeConversation,
        title: 'Deep Conversation',
        description: 'Complete a full conversation (send 5 messages)',
        status: DailyTaskStatus.notStarted,
        icon: Icons.forum_outlined,
        reward: 'Experience Points +15',
        targetCount: 5,
        createdAt: now,
      ),
      DailyTask(
        id: 'task_explore_discovery',
        type: DailyTaskType.exploreDiscovery,
        title: 'Explore Discovery',
        description: 'Browse the discovery page',
        status: DailyTaskStatus.notStarted,
        icon: Icons.explore,
        reward: 'Experience Points +5',
        createdAt: now,
      ),
    ];
  }

  /// 获取任务完成进度概览
  Future<Map<String, dynamic>> getTaskOverview() async {
    final allTasks = await getAllTasks();
    final completedTasks = allTasks.where((task) => task.isCompleted).length;
    final totalTasks = allTasks.length;
    final completionRate = totalTasks > 0 ? completedTasks / totalTasks : 0.0;

    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'activeTasks': totalTasks - completedTasks,
      'completionRate': completionRate,
    };
  }

  /// 清除所有任务数据（用于测试或重置）
  Future<void> clearAllData() async {
    await _preferences?.remove(_keyTasks);
    await _preferences?.remove(_keyLastResetDate);
    await _preferences?.remove(_keyTaskCompletionStats);
  }
}
