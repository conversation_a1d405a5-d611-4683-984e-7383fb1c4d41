import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';
import '../models/daily_task.dart';
import '../services/daily_task_service.dart';
import '../pages/daily_task_panel_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 每日任务卡片组件
/// 在探索页面显示当前活跃任务的简要信息，提供点击跳转功能
class DailyTaskCard extends StatefulWidget {
  const DailyTaskCard({super.key});

  @override
  State<DailyTaskCard> createState() => _DailyTaskCardState();
}

class _DailyTaskCardState extends State<DailyTaskCard> {
  List<DailyTask> _activeTasks = [];
  Map<String, dynamic> _taskOverview = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTaskData();
  }

  Future<void> _loadTaskData() async {
    try {
      final activeTasks = await DailyTaskService.instance.getActiveTasks();
      final overview = await DailyTaskService.instance.getTaskOverview();
      
      if (mounted) {
        setState(() {
          _activeTasks = activeTasks;
          _taskOverview = overview;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToTaskPanel() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DailyTaskPanelPage(),
      ),
    ).then((_) {
      // 从任务面板返回后刷新数据
      _loadTaskData();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingCard();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _navigateToTaskPanel,
          borderRadius: AppDesignSystem.borderRadiusL,
          child: Container(
            padding: const EdgeInsets.all(AppDesignSystem.spaceL),
            decoration: BoxDecoration(
              color: AppDesignSystem.backgroundCard,
              borderRadius: AppDesignSystem.borderRadiusL,
              border: Border.all(
                color: AppDesignSystem.primaryYellow.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppDesignSystem.primaryYellow.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: AppDesignSystem.spaceM),
                _buildProgressSection(),
                const SizedBox(height: AppDesignSystem.spaceM),
                _buildActiveTasksPreview(),
                const SizedBox(height: AppDesignSystem.spaceS),
                _buildViewAllButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
      padding: const EdgeInsets.all(AppDesignSystem.spaceL),
      decoration: BoxDecoration(
        color: AppDesignSystem.backgroundCard,
        borderRadius: AppDesignSystem.borderRadiusL,
        border: Border.all(
          color: AppDesignSystem.primaryYellow.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppDesignSystem.primaryYellow),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppDesignSystem.spaceS),
          decoration: BoxDecoration(
            color: AppDesignSystem.primaryYellow.withOpacity(0.2),
            borderRadius: AppDesignSystem.borderRadiusS,
          ),
          child: const Icon(
            Icons.task_alt,
            color: AppDesignSystem.primaryYellow,
            size: AppDesignSystem.iconSizeL,
          ),
        ),
        const SizedBox(width: AppDesignSystem.spaceM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context)!.dailyInteractiveTasks,
                style: const TextStyle(
                  color: AppDesignSystem.textPrimary,
                  fontSize: AppDesignSystem.fontSizeLarge,
                  fontWeight: AppDesignSystem.fontWeightSemiBold,
                ),
              ),
              const SizedBox(height: AppDesignSystem.spaceXXS),
              Text(
                AppLocalizations.of(context)!.completeTasksToEarnRewards,
                style: TextStyle(
                  color: AppDesignSystem.textSecondary,
                  fontSize: AppDesignSystem.fontSizeSmall,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.arrow_forward_ios,
          color: AppDesignSystem.textSecondary,
          size: AppDesignSystem.iconSizeS,
        ),
      ],
    );
  }

  Widget _buildProgressSection() {
    final totalTasks = _taskOverview['totalTasks'] ?? 0;
    final completedTasks = _taskOverview['completedTasks'] ?? 0;
    final completionRate = _taskOverview['completionRate'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(AppDesignSystem.spaceM),
      decoration: BoxDecoration(
        color: AppDesignSystem.backgroundPrimary,
        borderRadius: AppDesignSystem.borderRadiusS,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.todayProgress,
                  style: TextStyle(
                    color: AppDesignSystem.textSecondary,
                    fontSize: AppDesignSystem.fontSizeSmall,
                  ),
                ),
                const SizedBox(height: AppDesignSystem.spaceXXS),
                Text(
                  '$completedTasks/$totalTasks',
                  style: const TextStyle(
                    color: AppDesignSystem.textPrimary,
                    fontSize: AppDesignSystem.fontSizeLarge,
                    fontWeight: AppDesignSystem.fontWeightBold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: AppDesignSystem.spaceM),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.completionRate,
                      style: TextStyle(
                        color: AppDesignSystem.textSecondary,
                        fontSize: AppDesignSystem.fontSizeSmall,
                      ),
                    ),
                    Text(
                      '${(completionRate * 100).toInt()}%',
                      style: const TextStyle(
                        color: AppDesignSystem.primaryYellow,
                        fontSize: AppDesignSystem.fontSizeSmall,
                        fontWeight: AppDesignSystem.fontWeightMedium,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDesignSystem.spaceXXS),
                LinearProgressIndicator(
                  value: completionRate,
                  backgroundColor: AppDesignSystem.textSecondary.withOpacity(0.2),
                  valueColor: const AlwaysStoppedAnimation<Color>(AppDesignSystem.primaryYellow),
                  borderRadius: BorderRadius.circular(2),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveTasksPreview() {
    if (_activeTasks.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(AppDesignSystem.spaceM),
        decoration: BoxDecoration(
          color: AppDesignSystem.success.withOpacity(0.1),
          borderRadius: AppDesignSystem.borderRadiusS,
        ),
        child: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppDesignSystem.success,
              size: AppDesignSystem.iconSizeM,
            ),
            const SizedBox(width: AppDesignSystem.spaceS),
            Text(
              AppLocalizations.of(context)!.allTasksCompleted,
              style: const TextStyle(
                color: AppDesignSystem.success,
                fontSize: AppDesignSystem.fontSizeMedium,
                fontWeight: AppDesignSystem.fontWeightMedium,
              ),
            ),
          ],
        ),
      );
    }

    // 显示前3个活跃任务
    final previewTasks = _activeTasks.take(3).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.pendingTasks,
          style: TextStyle(
            color: AppDesignSystem.textSecondary,
            fontSize: AppDesignSystem.fontSizeSmall,
          ),
        ),
        const SizedBox(height: AppDesignSystem.spaceS),
        ...previewTasks.map((task) => _buildTaskPreviewItem(task)),
        if (_activeTasks.length > 3)
          Padding(
            padding: const EdgeInsets.only(top: AppDesignSystem.spaceXS),
            child: Text(
              '还有 ${_activeTasks.length - 3} 个任务...',
              style: TextStyle(
                color: AppDesignSystem.textSecondary,
                fontSize: AppDesignSystem.fontSizeSmall,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTaskPreviewItem(DailyTask task) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDesignSystem.spaceXS),
      child: Row(
        children: [
          Icon(
            task.icon,
            color: AppDesignSystem.textSecondary,
            size: AppDesignSystem.iconSizeS,
          ),
          const SizedBox(width: AppDesignSystem.spaceS),
          Expanded(
            child: Text(
              task.title,
              style: const TextStyle(
                color: AppDesignSystem.textPrimary,
                fontSize: AppDesignSystem.fontSizeRegular,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (task.targetCount > 1)
            Text(
              '${task.currentCount}/${task.targetCount}',
              style: TextStyle(
                color: AppDesignSystem.textSecondary,
                fontSize: AppDesignSystem.fontSizeSmall,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildViewAllButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: AppDesignSystem.spaceS),
      decoration: BoxDecoration(
        color: AppDesignSystem.primaryYellow.withOpacity(0.1),
        borderRadius: AppDesignSystem.borderRadiusS,
        border: Border.all(
          color: AppDesignSystem.primaryYellow.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        AppLocalizations.of(context)!.viewAllTasks,
        textAlign: TextAlign.center,
        style: const TextStyle(
          color: AppDesignSystem.primaryYellow,
          fontSize: AppDesignSystem.fontSizeMedium,
          fontWeight: AppDesignSystem.fontWeightMedium,
        ),
      ),
    );
  }
}
