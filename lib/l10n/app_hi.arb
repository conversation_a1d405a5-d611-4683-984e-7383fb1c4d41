{"@@locale": "hi", "appTitle": "AI चैट", "message": "संदेश", "recommend": "सिफारिश", "noChatsYet": "अभी तक कोई चैट नहीं।\nएक सुझाए गए उपयोगकर्ता पर टैप करें!", "aiRole": "AI भूमिका", "profile": "प्रोफाइल", "tapToEdit": "संपादित करने के लिए टैप करें", "settings": "सेटिंग्स", "language": "भाषा", "english": "अंग्रेजी", "traditionalChinese": "पारंपरिक चीनी", "arabic": "अरबी", "hindi": "हिंदी", "about": "के बारे में", "version": "संस्करण", "privacyPolicy": "गोपनीयता नीति", "viewOurPrivacyPolicy": "हमारी गोपनीयता नीति देखें", "termsOfService": "सेवा की शर्तें", "readyToChat": "चैट करने के लिए तैयार?", "whatSparksYourCuriosity": "आपकी जिज्ञासा क्या जगाती है?", "howCanIHelpYou": "मैं आज आपकी कैसे मदद कर सकता हूं?", "askMeAnything": "मुझसे कुछ भी पूछें...", "tellMeAboutYourself": "मुझे अपने बारे में बताएं", "whatsOnYourMind": "आपके दिमाग में क्या है?", "letsHaveAConversation": "आइए बातचीत करते हैं!", "report": "रिपोर्ट", "cancel": "रद्<PERSON> करें", "reportSubmittedSuccessfully": "रिपोर्ट सफलतापूर्वक सबमिट की गई", "serviceUnavailable": "सेवा अनुपलब्ध", "sorryEncounteredError": "क्षमा करें, मुझे एक त्रुटि का सामना करना पड़ा। कृपया पुनः प्रयास करें।", "pullUpToLoadMore": "अधिक लोड करने के लिए ऊपर खींचें", "releaseToLoadMore": "अधिक लोड करने के लिए छोड़ें", "loading": "लोड हो रहा है...", "loadCompleted": "लोड पूर्ण", "loadFailed": "लोड असफल", "noData": "-- कोई डेटा नहीं --", "thinking": "सोच रहा हूं...", "interests": "रुचियां", "startChatting": "चैट शुरू करें", "editUsername": "उपयोगकर्ता नाम संपादित करें", "username": "उपयोगकर्ता नाम", "save": "सेव करें", "enterYourUsername": "अपना उपयोगकर्ता नाम दर्ज करें", "usernameCannotBeEmpty": "उपयोगकर्ता नाम खाली नहीं हो सकता", "errorSavingUsername": "उपयोगकर्ता नाम सेव करने में त्रुटि", "usernameDescription": "आपका उपयोगकर्ता नाम ऐप में प्रदर्शित होगा और कभी भी बदला जा सकता है।", "chatNow": "अभी चैट करें", "pullToRefresh": "रिफ्रेश करने के लिए खींचें", "releaseToRefresh": "रिफ्रेश करने के लिए छोड़ें", "refreshing": "रिफ्रेश हो रहा है...", "refreshCompleted": "रिफ्रेश पूर्ण", "noMoreData": "कोई और डेटा नहीं", "refreshFailed": "रिफ्रेश असफल", "lastUpdatedAt": "अंतिम अपडेट %T पर", "viewTermsOfService": "सेवा की शर्तें देखें", "yesterday": "कल", "selectAvatar": "अवतार चुनें", "chooseYourAvatar": "अपना अवतार चुनें", "noAvatarsAvailable": "कोई अवतार उपलब्ध नहीं", "avatarLoadError": "अवतार लोड करने में विफल", "avatarSelectionError": "अवतार चुनने में त्रुटि", "discovery": "खोज", "weeklyStars": "सप्ताह के सितारे", "categoryRecommendations": "श्रेणी सुझाव", "hotTopics": "लोकप्रिय विषय", "entertainment": "मनोरंजन", "learning": "सीखना", "work": "काम", "lifestyle": "जीवनशैली", "technology": "प्रौद्योगिकी", "creativity": "रचनात्मकता", "viewMore": "और देखें", "startChat": "चैट शुरू करें", "popularThisWeek": "इस सप्ताह लोकप्रिय", "trendingNow": "अभी ट्रेंडिंग", "exploreCategory": "श्रेणी देखें", "joinDiscussion": "चर्चा में शामिल हों", "topicAIFuture": "AI विकास का भविष्य", "topicProductivity": "कार्य दक्षता कैसे बढ़ाएं", "topicCreativeWriting": "रचनात्मक लेखन तकनीकें", "topicHealthyLifestyle": "स्वस्थ जीवनशैली", "topicTechLife": "प्रौद्योगिकी और जीवन", "topicPersonalGrowth": "व्यक्तिगत विकास और प्रगति", "dailyTopics": "आज के विषय", "topicTeacherGoals": "लक्ष्य पर चर्चा", "topicArtistPainting": "पेंटिंग सीखें", "topicHeartbreakAdvice": "दिल टूटने से निपटें", "topicCookingTips": "खाना बनाना सीखें", "topicFitnessMotivation": "फिटनेस शुरू करें", "topicTravelStories": "यात्रा साझा करें", "topicMusicDiscovery": "संगीत खोजें", "topicBookRecommendations": "किताबें खोजें", "topicCareerAdvice": "करियर की योजना", "topicMindfulness": "माइंडफुलनेस अभ्यास", "topicTeacherGoalsDetail": "मैं जीवन के लक्ष्य निर्धारित करने और उन्हें प्राप्त करने के बारे में बात करना चाहता हूं", "topicArtistPaintingDetail": "मैं पेंटिंग तकनीक सीखना चाहता हूं, क्या आप मुझे कुछ बुनियादी बातें सिखा सकते हैं?", "topicHeartbreakAdviceDetail": "मैं हाल ही में ब्रेकअप से गुजरा हूं और उदास महसूस कर रहा हूं, क्या आप मुझे कुछ सलाह दे सकते हैं?", "topicCookingTipsDetail": "मैं कुछ सरल घरेलू व्यंजन सीखना चाहता हूं, आप क्या सुझाते हैं?", "topicFitnessMotivationDetail": "मैं व्यायाम शुरू करना चाहता हूं लेकिन प्रेरणा की कमी है, क्या आप मुझे एक योजना बनाने में मदद कर सकते हैं?", "topicTravelStoriesDetail": "मैं आपके यात्रा अनुभवों के बारे में सुनना चाहूंगा, या क्या आप कुछ जगहों की सिफारिश कर सकते हैं जो देखने लायक हैं?", "topicMusicDiscoveryDetail": "मैं नई संगीत शैलियों की खोज करना चाहता हूं, क्या आप कुछ अच्छे गाने सुझा सकते हैं?", "topicBookRecommendationsDetail": "मैं पढ़ने के लिए अच्छी किताबें ढूंढ रहा हूं, क्या आप मेरी रुचियों के आधार पर कुछ सुझा सकते हैं?", "topicCareerAdviceDetail": "मैं अपने करियर विकास के बारे में भ्रमित हूं, क्या आप मुझे कुछ सलाह दे सकते हैं?", "topicMindfulnessDetail": "मैं तनाव कम करने के लिए ध्यान और माइंडफुलनेस अभ्यास सीखना चाहता हूं, क्या आप मुझे कुछ तरीके सिखा सकते हैं?", "searchChats": "चैट खोजें...", "iceBreakers": "बर्फ तोड़ने वाले", "tapToSendMessage": "संदेश भेजने के लिए टैप करें", "iceBreakerHello": "नमस्ते, क्या आप अपना परिचय दे सकते हैं?", "iceBreakerInteresting": "आइए कुछ दिलचस्प बात करते हैं!", "iceBreakerFavoriteTopic": "आपका पसंदीदा चर्चा का विषय क्या है?", "iceBreakerUnique": "मुझे कुछ ऐसा बताएं जो आपको अनोखा बनाता है", "iceBreakerThinking": "हाल ही में आपके मन में क्या चल रहा है?", "top3Picks": "इस सप्ताह के टॉप 3 चुनिंदा", "topPicks": "टॉप चुनिंदा", "weeklyTop3": "साप्ताहिक टॉप 3", "mostPopularThisWeek": "इस सप्ताह सबसे लोकप्रिय", "rank1": "पहला स्थान", "rank2": "दूसरा स्थान", "rank3": "तीसरा स्थान", "champion": "चैंपियन", "runnerUp": "उपविजेता", "thirdPlace": "तीसरा स्थान", "popularityRanking": "लोकप्रियता रैंकिंग", "seeAll": "सभी देखें", "fullRankings": "पूर्ण रैंकिंग", "aiCharacterRankings": "AI चरित्र रैंकिंग", "allCharacters": "सभी पात्र", "fictional": "काल्पनिक", "newLabel": "नया", "filters": "फिल्टर", "searchCharacters": "पात्रों को खोजें...", "view": "देखें", "score": "स्कोर", "selectEmoji": "इमोजी चुनें", "emojiCategorySmiling": "मुस्कुराते हुए", "emojiCategoryEmotions": "भावनाएं", "emojiCategoryGestures": "इशारे", "emojiCategoryHearts": "दिल", "emojiCategoryAnimals": "ज<PERSON><PERSON><PERSON>र", "emojiCategoryFood": "खाना"}