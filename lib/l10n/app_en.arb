{"@@locale": "en", "appTitle": "AI Chat", "message": "Message", "recommend": "Recommend", "noChatsYet": "No chats yet.\nTap on a recommended user!", "aiRole": "AI Role", "profile": "Profile", "tapToEdit": "Tap to edit", "settings": "Settings", "language": "Language", "english": "English", "traditionalChinese": "Traditional Chinese", "arabic": "Arabic", "hindi": "Hindi", "about": "About", "version": "Version", "privacyPolicy": "Privacy Policy", "viewOurPrivacyPolicy": "View our privacy policy", "termsOfService": "Terms of Service", "readyToChat": "Ready to chat?", "whatSparksYourCuriosity": "What sparks your curiosity?", "howCanIHelpYou": "How can I help you today?", "askMeAnything": "Ask me anything...", "tellMeAboutYourself": "Tell me about yourself", "whatsOnYourMind": "What's on your mind?", "letsHaveAConversation": "Let's have a conversation!", "report": "Report", "cancel": "Cancel", "reportSubmittedSuccessfully": "Report submitted successfully", "serviceUnavailable": "Service unavailable", "sorryEncounteredError": "Sorry, I encountered an error. Please try again.", "pullUpToLoadMore": "Pull up to load more", "releaseToLoadMore": "Release to load more", "loading": "Loading...", "loadCompleted": "Load completed", "loadFailed": "Load failed", "noData": "-- No Data --", "thinking": "Thinking...", "interests": "Interests", "startChatting": "Start Chatting", "editUsername": "Edit Username", "username": "Username", "save": "Save", "enterYourUsername": "Enter your username", "usernameCannotBeEmpty": "Username cannot be empty", "errorSavingUsername": "Error saving username", "usernameDescription": "Your username will be displayed in the app and can be changed anytime.", "chatNow": "Chat Now", "pullToRefresh": "Pull to refresh", "releaseToRefresh": "Release to refresh", "refreshing": "Refreshing...", "refreshCompleted": "Refresh completed", "noMoreData": "No more data", "refreshFailed": "Refresh failed", "lastUpdatedAt": "Last updated at %T", "viewTermsOfService": "View terms of service", "yesterday": "Yesterday", "selectAvatar": "Select Avatar", "chooseYourAvatar": "Choose your avatar", "noAvatarsAvailable": "No avatars available", "avatarLoadError": "Failed to load avatar", "avatarSelectionError": "Error selecting avatar", "discovery": "Discovery", "weeklyStars": "Weekly Stars", "categoryRecommendations": "Category Recommendations", "hotTopics": "Hot Topics", "entertainment": "Entertainment", "learning": "Learning", "work": "Work", "lifestyle": "Lifestyle", "technology": "Technology", "creativity": "Creativity", "viewMore": "View More", "startChat": "Start Chat", "popularThisWeek": "Popular This Week", "trendingNow": "Trending Now", "exploreCategory": "Explore Category", "joinDiscussion": "Join <PERSON>", "topicAIFuture": "Future of AI Development", "topicProductivity": "How to Improve Work Efficiency", "topicCreativeWriting": "Creative Writing Techniques", "topicHealthyLifestyle": "Healthy Lifestyle", "topicTechLife": "Technology and Life", "topicPersonalGrowth": "Personal Growth and Development", "dailyTopics": "Daily Topics", "topicTeacherGoals": "Discuss Goals", "topicArtistPainting": "Learn Painting", "topicHeartbreakAdvice": "Handle Heartbreak", "topicCookingTips": "Learn Cooking", "topicFitnessMotivation": "Start Fitness", "topicTravelStories": "Share Travel", "topicMusicDiscovery": "Discover Music", "topicBookRecommendations": "Find Books", "topicCareerAdvice": "Plan Career", "topicMindfulness": "Practice Mindfulness", "topicTeacherGoalsDetail": "I want to talk about how to set and achieve life goals", "topicArtistPaintingDetail": "I want to learn painting techniques, can you teach me some basics?", "topicHeartbreakAdviceDetail": "I recently went through a breakup and feel sad, can you give me some advice?", "topicCookingTipsDetail": "I want to learn some simple home-cooked dishes, what do you recommend?", "topicFitnessMotivationDetail": "I want to start exercising but lack motivation, can you help me create a plan?", "topicTravelStoriesDetail": "I'd like to hear about your travel experiences, or can you recommend some places worth visiting?", "topicMusicDiscoveryDetail": "I want to discover new music genres, can you recommend some good songs?", "topicBookRecommendationsDetail": "I'm looking for good books to read, can you recommend some based on my interests?", "topicCareerAdviceDetail": "I'm confused about my career development, can you give me some advice?", "topicMindfulnessDetail": "I want to learn meditation and mindfulness practices to reduce stress, can you teach me some methods?", "searchChats": "Search chats...", "iceBreakers": "Ice Breakers", "tapToSendMessage": "Tap to send message", "iceBreakerHello": "Hello, can you introduce yourself?", "iceBreakerInteresting": "Let's chat about something interesting!", "iceBreakerFavoriteTopic": "What's your favorite topic to discuss?", "iceBreakerUnique": "Tell me something that makes you unique", "iceBreakerThinking": "What's been on your mind lately?", "top3Picks": "Top 3 Picks This Week", "topPicks": "Top Picks", "weeklyTop3": "Weekly Top 3", "mostPopularThisWeek": "Most Popular This Week", "rank1": "1st Place", "rank2": "2nd Place", "rank3": "3rd Place", "champion": "Champion", "runnerUp": "Runner-up", "thirdPlace": "Third Place", "popularityRanking": "Popularity Ranking", "seeAll": "See All", "fullRankings": "Full Rankings", "aiCharacterRankings": "AI Character Rankings", "allCharacters": "All characters", "fictional": "Fictional", "newLabel": "New", "filters": "Filters", "searchCharacters": "Search characters...", "view": "View", "score": "Score", "selectEmoji": "Select Emoji", "emojiCategorySmiling": "Smiling", "emojiCategoryEmotions": "Emotions", "emojiCategoryGestures": "Gestures", "emojiCategoryHearts": "Hearts", "emojiCategoryAnimals": "Animals", "emojiCategoryFood": "Food", "dailyTasks": "Daily Tasks", "dailyInteractiveTasks": "Daily Interactive Tasks", "completeTasksToEarnRewards": "Complete tasks to earn rewards", "todayProgress": "Today's Progress", "completionRate": "Completion Rate", "totalTasks": "Total Tasks", "completed": "Completed", "inProgress": "In Progress", "pendingTasks": "Pending Tasks", "allTasksCompleted": "All tasks completed today!", "viewAllTasks": "View All Tasks", "reward": "<PERSON><PERSON>", "progress": "Progress", "taskStartChat": "Start Conversation", "taskStartChatDesc": "Start your first conversation with any AI character", "taskRadarScan": "Radar Exploration", "taskRadarScanDesc": "Use the radar scan feature once", "taskViewProfile": "Personal Profile", "taskViewProfileDesc": "Browse your personal profile page", "taskUseSearch": "Search Exploration", "taskUseSearchDesc": "Use the search function to find content", "taskViewMessages": "Message Center", "taskViewMessagesDesc": "Check your message list", "taskCompleteConversation": "Deep Conversation", "taskCompleteConversationDesc": "Complete a full conversation (send 5 messages)", "taskExploreDiscovery": "Explore Discovery", "taskExploreDiscoveryDesc": "Browse the discovery page", "experiencePoints": "Experience Points"}